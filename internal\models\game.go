package models

import (
	"encoding/json"
	"time"
)

type GameStatus string

const (
	GameStatusWaiting    GameStatus = "waiting"
	GameStatusInProgress GameStatus = "in_progress"
	GameStatusFinished   GameStatus = "finished"
	GameStatusAbandoned  GameStatus = "abandoned"
)

type GameType string

const (
	GameTypeTicTacToe GameType = "tictactoe"
)

type Game struct {
	ID        int        `json:"id"`
	Player1ID int        `json:"player1_id"`
	Player2ID *int       `json:"player2_id"`
	GameType  GameType   `json:"game_type"`
	GameState string     `json:"game_state"` // JSON string
	Status    GameStatus `json:"status"`
	WinnerID  *int       `json:"winner_id"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
}

type GameMove struct {
	ID         int       `json:"id"`
	GameID     int       `json:"game_id"`
	PlayerID   int       `json:"player_id"`
	MoveData   string    `json:"move_data"` // JSON string
	MoveNumber int       `json:"move_number"`
	CreatedAt  time.Time `json:"created_at"`
}

// TicTacToe specific structures
type TicTacToeState struct {
	Board       [3][3]string `json:"board"`
	CurrentTurn int          `json:"current_turn"`
	GameOver    bool         `json:"game_over"`
	Winner      *int         `json:"winner"`
	IsDraw      bool         `json:"is_draw"`
}

type TicTacToeMove struct {
	Row    int `json:"row"`
	Col    int `json:"col"`
	Player int `json:"player"`
}

type GameResponse struct {
	ID        int        `json:"id"`
	Player1   *User      `json:"player1,omitempty"`
	Player2   *User      `json:"player2,omitempty"`
	GameType  GameType   `json:"game_type"`
	GameState string     `json:"game_state"`
	Status    GameStatus `json:"status"`
	Winner    *User      `json:"winner,omitempty"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
}

func NewTicTacToeState() *TicTacToeState {
	return &TicTacToeState{
		Board:       [3][3]string{},
		CurrentTurn: 1,
		GameOver:    false,
		Winner:      nil,
		IsDraw:      false,
	}
}

func (g *Game) GetTicTacToeState() (*TicTacToeState, error) {
	var state TicTacToeState
	if err := json.Unmarshal([]byte(g.GameState), &state); err != nil {
		return nil, err
	}
	return &state, nil
}

func (g *Game) SetTicTacToeState(state *TicTacToeState) error {
	stateBytes, err := json.Marshal(state)
	if err != nil {
		return err
	}
	g.GameState = string(stateBytes)
	return nil
}
