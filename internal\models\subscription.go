package models

import (
	"time"
)

type Subscription struct {
	ID                   int       `json:"id"`
	UserID               int       `json:"user_id"`
	StripeSubscriptionID string    `json:"stripe_subscription_id"`
	StripePriceID        string    `json:"stripe_price_id"`
	Status               string    `json:"status"`
	CurrentPeriodStart   time.Time `json:"current_period_start"`
	CurrentPeriodEnd     time.Time `json:"current_period_end"`
	CreatedAt            time.Time `json:"created_at"`
	UpdatedAt            time.Time `json:"updated_at"`
}

type SubscriptionResponse struct {
	ID                 int       `json:"id"`
	Status             string    `json:"status"`
	CurrentPeriodStart time.Time `json:"current_period_start"`
	CurrentPeriodEnd   time.Time `json:"current_period_end"`
	CreatedAt          time.Time `json:"created_at"`
}

func (s *Subscription) ToResponse() SubscriptionResponse {
	return SubscriptionResponse{
		ID:                 s.ID,
		Status:             s.Status,
		CurrentPeriodStart: s.CurrentPeriodStart,
		CurrentPeriodEnd:   s.CurrentPeriodEnd,
		CreatedAt:          s.CreatedAt,
	}
}

func (s *Subscription) IsActive() bool {
	return s.Status == "active" && time.Now().Before(s.CurrentPeriodEnd)
}
