package database

import (
	"database/sql"
	"fmt"
	"gogamesws/internal/models"
	"time"
)

// User queries
func CreateUser(email, passwordHash string) (*models.User, error) {
	query := `
		INSERT INTO users (email, password_hash, role, subscription_status, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6)
		RETURNING id, email, role, subscription_status, created_at, updated_at
	`
	
	user := &models.User{}
	now := time.Now()
	
	err := DB.QueryRow(query, email, passwordHash, models.RoleSpectator, models.SubscriptionInactive, now, now).Scan(
		&user.ID, &user.Email, &user.Role, &user.SubscriptionStatus, &user.CreatedAt, &user.UpdatedAt,
	)
	
	if err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}
	
	return user, nil
}

func GetUserByEmail(email string) (*models.User, error) {
	query := `
		SELECT id, email, password_hash, role, subscription_status, created_at, updated_at
		FROM users WHERE email = $1
	`
	
	user := &models.User{}
	err := DB.QueryRow(query, email).Scan(
		&user.ID, &user.Email, &user.PasswordHash, &user.Role, &user.SubscriptionStatus, &user.CreatedAt, &user.UpdatedAt,
	)
	
	if err != nil {
		return nil, fmt.Errorf("failed to get user by email: %w", err)
	}
	
	return user, nil
}

func GetUserByID(id int) (*models.User, error) {
	query := `
		SELECT id, email, password_hash, role, subscription_status, created_at, updated_at
		FROM users WHERE id = $1
	`
	
	user := &models.User{}
	err := DB.QueryRow(query, id).Scan(
		&user.ID, &user.Email, &user.PasswordHash, &user.Role, &user.SubscriptionStatus, &user.CreatedAt, &user.UpdatedAt,
	)
	
	if err != nil {
		return nil, fmt.Errorf("failed to get user by ID: %w", err)
	}
	
	return user, nil
}

func UpdateUserSubscriptionStatus(userID int, status models.SubscriptionStatus) error {
	query := `UPDATE users SET subscription_status = $1, updated_at = $2 WHERE id = $3`
	_, err := DB.Exec(query, status, time.Now(), userID)
	return err
}

// Game queries
func CreateGame(player1ID int, gameType models.GameType, gameState string) (*models.Game, error) {
	query := `
		INSERT INTO games (player1_id, game_type, game_state, status, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6)
		RETURNING id, player1_id, player2_id, game_type, game_state, status, winner_id, created_at, updated_at
	`
	
	game := &models.Game{}
	now := time.Now()
	
	err := DB.QueryRow(query, player1ID, gameType, gameState, models.GameStatusWaiting, now, now).Scan(
		&game.ID, &game.Player1ID, &game.Player2ID, &game.GameType, &game.GameState, &game.Status, &game.WinnerID, &game.CreatedAt, &game.UpdatedAt,
	)
	
	if err != nil {
		return nil, fmt.Errorf("failed to create game: %w", err)
	}
	
	return game, nil
}

func GetGameByID(id int) (*models.Game, error) {
	query := `
		SELECT id, player1_id, player2_id, game_type, game_state, status, winner_id, created_at, updated_at
		FROM games WHERE id = $1
	`
	
	game := &models.Game{}
	err := DB.QueryRow(query, id).Scan(
		&game.ID, &game.Player1ID, &game.Player2ID, &game.GameType, &game.GameState, &game.Status, &game.WinnerID, &game.CreatedAt, &game.UpdatedAt,
	)
	
	if err != nil {
		return nil, fmt.Errorf("failed to get game by ID: %w", err)
	}
	
	return game, nil
}

func UpdateGame(game *models.Game) error {
	query := `
		UPDATE games 
		SET player2_id = $1, game_state = $2, status = $3, winner_id = $4, updated_at = $5
		WHERE id = $6
	`
	
	_, err := DB.Exec(query, game.Player2ID, game.GameState, game.Status, game.WinnerID, time.Now(), game.ID)
	return err
}

func GetActiveGames() ([]*models.Game, error) {
	query := `
		SELECT id, player1_id, player2_id, game_type, game_state, status, winner_id, created_at, updated_at
		FROM games 
		WHERE status IN ($1, $2)
		ORDER BY created_at DESC
	`
	
	rows, err := DB.Query(query, models.GameStatusWaiting, models.GameStatusInProgress)
	if err != nil {
		return nil, fmt.Errorf("failed to get active games: %w", err)
	}
	defer rows.Close()
	
	var games []*models.Game
	for rows.Next() {
		game := &models.Game{}
		err := rows.Scan(
			&game.ID, &game.Player1ID, &game.Player2ID, &game.GameType, &game.GameState, &game.Status, &game.WinnerID, &game.CreatedAt, &game.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan game: %w", err)
		}
		games = append(games, game)
	}
	
	return games, nil
}

// Game moves queries
func CreateGameMove(gameID, playerID int, moveData string, moveNumber int) (*models.GameMove, error) {
	query := `
		INSERT INTO game_moves (game_id, player_id, move_data, move_number, created_at)
		VALUES ($1, $2, $3, $4, $5)
		RETURNING id, game_id, player_id, move_data, move_number, created_at
	`
	
	move := &models.GameMove{}
	now := time.Now()
	
	err := DB.QueryRow(query, gameID, playerID, moveData, moveNumber, now).Scan(
		&move.ID, &move.GameID, &move.PlayerID, &move.MoveData, &move.MoveNumber, &move.CreatedAt,
	)
	
	if err != nil {
		return nil, fmt.Errorf("failed to create game move: %w", err)
	}
	
	return move, nil
}

// Subscription queries
func CreateSubscription(userID int, stripeSubID, stripePriceID string, status string, periodStart, periodEnd time.Time) (*models.Subscription, error) {
	query := `
		INSERT INTO subscriptions (user_id, stripe_subscription_id, stripe_price_id, status, current_period_start, current_period_end, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
		RETURNING id, user_id, stripe_subscription_id, stripe_price_id, status, current_period_start, current_period_end, created_at, updated_at
	`
	
	sub := &models.Subscription{}
	now := time.Now()
	
	err := DB.QueryRow(query, userID, stripeSubID, stripePriceID, status, periodStart, periodEnd, now, now).Scan(
		&sub.ID, &sub.UserID, &sub.StripeSubscriptionID, &sub.StripePriceID, &sub.Status, &sub.CurrentPeriodStart, &sub.CurrentPeriodEnd, &sub.CreatedAt, &sub.UpdatedAt,
	)
	
	if err != nil {
		return nil, fmt.Errorf("failed to create subscription: %w", err)
	}
	
	return sub, nil
}

func GetSubscriptionByUserID(userID int) (*models.Subscription, error) {
	query := `
		SELECT id, user_id, stripe_subscription_id, stripe_price_id, status, current_period_start, current_period_end, created_at, updated_at
		FROM subscriptions WHERE user_id = $1 ORDER BY created_at DESC LIMIT 1
	`
	
	sub := &models.Subscription{}
	err := DB.QueryRow(query, userID).Scan(
		&sub.ID, &sub.UserID, &sub.StripeSubscriptionID, &sub.StripePriceID, &sub.Status, &sub.CurrentPeriodStart, &sub.CurrentPeriodEnd, &sub.CreatedAt, &sub.UpdatedAt,
	)
	
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get subscription by user ID: %w", err)
	}
	
	return sub, nil
}

func UpdateSubscription(sub *models.Subscription) error {
	query := `
		UPDATE subscriptions 
		SET status = $1, current_period_start = $2, current_period_end = $3, updated_at = $4
		WHERE id = $5
	`
	
	_, err := DB.Exec(query, sub.Status, sub.CurrentPeriodStart, sub.CurrentPeriodEnd, time.Now(), sub.ID)
	return err
}
