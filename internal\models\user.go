package models

import (
	"time"
)

type UserRole string

const (
	RoleSpectator UserRole = "spectator"
	RolePlayer    UserRole = "player"
)

type SubscriptionStatus string

const (
	SubscriptionActive   SubscriptionStatus = "active"
	SubscriptionInactive SubscriptionStatus = "inactive"
	SubscriptionCanceled SubscriptionStatus = "canceled"
)

type User struct {
	ID                 int                `json:"id"`
	Email              string             `json:"email"`
	PasswordHash       string             `json:"-"`
	Role               UserRole           `json:"role"`
	SubscriptionStatus SubscriptionStatus `json:"subscription_status"`
	CreatedAt          time.Time          `json:"created_at"`
	UpdatedAt          time.Time          `json:"updated_at"`
}

type UserRegistration struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

type UserLogin struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

type UserResponse struct {
	ID                 int                `json:"id"`
	Email              string             `json:"email"`
	Role               UserRole           `json:"role"`
	SubscriptionStatus SubscriptionStatus `json:"subscription_status"`
	CreatedAt          time.Time          `json:"created_at"`
}

func (u *User) ToResponse() UserResponse {
	return UserResponse{
		ID:                 u.ID,
		Email:              u.Email,
		Role:               u.Role,
		SubscriptionStatus: u.SubscriptionStatus,
		CreatedAt:          u.CreatedAt,
	}
}

func (u *User) CanPlay() bool {
	return u.SubscriptionStatus == SubscriptionActive
}

func (u *User) IsSpectator() bool {
	return u.Role == RoleSpectator || !u.CanPlay()
}
